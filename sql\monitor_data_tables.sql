-- =============================================
-- 监控数据持久化表结构
-- 创建时间：2025-01-31
-- 说明：用于存储监控历史数据和告警记录
-- =============================================

-- 1. 系统性能监控历史表
CREATE TABLE IF NOT EXISTS sys_monitor_performance (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    record_time DATETIME NOT NULL COMMENT '记录时间',
    memory_used BIGINT COMMENT '已使用内存(字节)',
    memory_total BIGINT COMMENT '总内存(字节)',
    memory_usage_percentage DECIMAL(5,2) COMMENT '内存使用率(%)',
    heap_used BIGINT COMMENT '堆内存使用量(字节)',
    heap_max BIGINT COMMENT '最大堆内存(字节)',
    non_heap_used BIGINT COMMENT '非堆内存使用量(字节)',
    db_response_time INT COMMENT '数据库响应时间(ms)',
    db_active_connections INT COMMENT '数据库活跃连接数',
    db_healthy TINYINT(1) COMMENT '数据库健康状态(0-异常,1-正常)',
    redis_response_time INT COMMENT 'Redis响应时间(ms)',
    redis_healthy TINYINT(1) COMMENT 'Redis健康状态(0-异常,1-正常)',
    system_healthy TINYINT(1) COMMENT '系统整体健康状态(0-异常,1-正常)',
    cpu_usage_percentage DECIMAL(5,2) COMMENT 'CPU使用率(%)',
    disk_usage_percentage DECIMAL(5,2) COMMENT '磁盘使用率(%)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_record_time (record_time),
    INDEX idx_system_healthy (system_healthy),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统性能监控历史表';

-- 2. 告警记录表
CREATE TABLE IF NOT EXISTS sys_monitor_alert (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    alert_type VARCHAR(50) NOT NULL COMMENT '告警类型(MEMORY,DATABASE,REDIS,SYSTEM)',
    alert_level VARCHAR(20) NOT NULL COMMENT '告警级别(INFO,WARNING,ERROR,CRITICAL)',
    alert_title VARCHAR(200) NOT NULL COMMENT '告警标题',
    alert_message TEXT COMMENT '告警详细信息',
    alert_value VARCHAR(100) COMMENT '触发告警的值',
    threshold_value VARCHAR(100) COMMENT '阈值',
    source_ip VARCHAR(50) COMMENT '来源IP',
    source_module VARCHAR(100) COMMENT '来源模块',
    alert_time DATETIME NOT NULL COMMENT '告警时间',
    resolved_time DATETIME COMMENT '解决时间',
    alert_status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '告警状态(ACTIVE,RESOLVED,IGNORED)',
    notification_sent TINYINT(1) DEFAULT 0 COMMENT '是否已发送通知(0-否,1-是)',
    notification_type VARCHAR(50) COMMENT '通知方式(EMAIL,SMS,WEBHOOK)',
    handler VARCHAR(100) COMMENT '处理人',
    remark TEXT COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_alert_type (alert_type),
    INDEX idx_alert_level (alert_level),
    INDEX idx_alert_time (alert_time),
    INDEX idx_alert_status (alert_status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统告警记录表';

-- 3. 监控配置表
CREATE TABLE IF NOT EXISTS sys_monitor_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(50) COMMENT '配置类型(THRESHOLD,NOTIFICATION,SCHEDULE)',
    config_group VARCHAR(50) COMMENT '配置分组(MEMORY,DATABASE,REDIS,EMAIL)',
    description VARCHAR(500) COMMENT '配置描述',
    is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用(0-禁用,1-启用)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_type (config_type),
    INDEX idx_config_group (config_group),
    INDEX idx_is_enabled (is_enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控配置表';

-- 4. 监控统计汇总表
CREATE TABLE IF NOT EXISTS sys_monitor_statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    stat_hour TINYINT COMMENT '统计小时(0-23,NULL表示全天)',
    total_alerts INT DEFAULT 0 COMMENT '总告警数',
    memory_alerts INT DEFAULT 0 COMMENT '内存告警数',
    database_alerts INT DEFAULT 0 COMMENT '数据库告警数',
    redis_alerts INT DEFAULT 0 COMMENT 'Redis告警数',
    system_alerts INT DEFAULT 0 COMMENT '系统告警数',
    avg_memory_usage DECIMAL(5,2) COMMENT '平均内存使用率(%)',
    max_memory_usage DECIMAL(5,2) COMMENT '最大内存使用率(%)',
    avg_db_response_time INT COMMENT '平均数据库响应时间(ms)',
    max_db_response_time INT COMMENT '最大数据库响应时间(ms)',
    avg_redis_response_time INT COMMENT '平均Redis响应时间(ms)',
    max_redis_response_time INT COMMENT '最大Redis响应时间(ms)',
    system_uptime_percentage DECIMAL(5,2) COMMENT '系统正常运行时间百分比',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_stat_date_hour (stat_date, stat_hour),
    INDEX idx_stat_date (stat_date),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控统计汇总表';

-- 5. 插入默认监控配置
INSERT INTO sys_monitor_config (config_key, config_value, config_type, config_group, description) VALUES
('monitor.memory.threshold', '85', 'THRESHOLD', 'MEMORY', '内存使用率告警阈值(%)'),
('monitor.database.response.threshold', '5000', 'THRESHOLD', 'DATABASE', '数据库响应时间告警阈值(ms)'),
('monitor.redis.response.threshold', '5000', 'THRESHOLD', 'REDIS', 'Redis响应时间告警阈值(ms)'),
('monitor.alert.cooldown.minutes', '30', 'THRESHOLD', 'ALERT', '告警冷却时间(分钟)'),
('monitor.email.enabled', 'false', 'NOTIFICATION', 'EMAIL', '是否启用邮件告警'),
('monitor.email.to', '<EMAIL>', 'NOTIFICATION', 'EMAIL', '告警邮件接收地址'),
('monitor.email.from', '<EMAIL>', 'NOTIFICATION', 'EMAIL', '告警邮件发送地址'),
('monitor.schedule.performance.interval', '60', 'SCHEDULE', 'PERFORMANCE', '性能监控检查间隔(秒)'),
('monitor.schedule.alert.interval', '300', 'SCHEDULE', 'ALERT', '告警检查间隔(秒)'),
('monitor.data.retention.days', '30', 'SCHEDULE', 'DATA', '监控数据保留天数')
ON DUPLICATE KEY UPDATE 
    config_value = VALUES(config_value),
    description = VALUES(description),
    update_time = CURRENT_TIMESTAMP;

-- 6. 创建数据清理存储过程
DELIMITER $$

CREATE PROCEDURE CleanOldMonitorData()
BEGIN
    DECLARE retention_days INT DEFAULT 30;
    
    -- 获取数据保留天数配置
    SELECT CAST(config_value AS UNSIGNED) INTO retention_days 
    FROM sys_monitor_config 
    WHERE config_key = 'monitor.data.retention.days' AND is_enabled = 1
    LIMIT 1;
    
    -- 清理过期的性能监控数据
    DELETE FROM sys_monitor_performance 
    WHERE create_time < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 清理过期的已解决告警记录
    DELETE FROM sys_monitor_alert 
    WHERE alert_status = 'RESOLVED' 
    AND resolved_time < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 清理过期的统计数据(保留更长时间)
    DELETE FROM sys_monitor_statistics 
    WHERE create_time < DATE_SUB(NOW(), INTERVAL (retention_days * 3) DAY);
    
    -- 记录清理日志
    INSERT INTO sys_monitor_alert (
        alert_type, alert_level, alert_title, alert_message, 
        alert_time, alert_status, notification_sent
    ) VALUES (
        'SYSTEM', 'INFO', '监控数据清理', 
        CONCAT('已清理超过', retention_days, '天的监控数据'), 
        NOW(), 'RESOLVED', 0
    );
    
END$$

DELIMITER ;

-- 7. 创建统计汇总存储过程
DELIMITER $$

CREATE PROCEDURE GenerateMonitorStatistics(IN stat_date DATE)
BEGIN
    -- 生成每日统计
    INSERT INTO sys_monitor_statistics (
        stat_date, stat_hour, total_alerts, memory_alerts, database_alerts, 
        redis_alerts, system_alerts, avg_memory_usage, max_memory_usage,
        avg_db_response_time, max_db_response_time, avg_redis_response_time, 
        max_redis_response_time, system_uptime_percentage
    )
    SELECT 
        stat_date,
        NULL as stat_hour,
        COUNT(a.id) as total_alerts,
        SUM(CASE WHEN a.alert_type = 'MEMORY' THEN 1 ELSE 0 END) as memory_alerts,
        SUM(CASE WHEN a.alert_type = 'DATABASE' THEN 1 ELSE 0 END) as database_alerts,
        SUM(CASE WHEN a.alert_type = 'REDIS' THEN 1 ELSE 0 END) as redis_alerts,
        SUM(CASE WHEN a.alert_type = 'SYSTEM' THEN 1 ELSE 0 END) as system_alerts,
        AVG(p.memory_usage_percentage) as avg_memory_usage,
        MAX(p.memory_usage_percentage) as max_memory_usage,
        AVG(p.db_response_time) as avg_db_response_time,
        MAX(p.db_response_time) as max_db_response_time,
        AVG(p.redis_response_time) as avg_redis_response_time,
        MAX(p.redis_response_time) as max_redis_response_time,
        (SUM(CASE WHEN p.system_healthy = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(p.id)) as system_uptime_percentage
    FROM 
        (SELECT stat_date) d
    LEFT JOIN sys_monitor_alert a ON DATE(a.alert_time) = stat_date
    LEFT JOIN sys_monitor_performance p ON DATE(p.record_time) = stat_date
    GROUP BY stat_date
    ON DUPLICATE KEY UPDATE
        total_alerts = VALUES(total_alerts),
        memory_alerts = VALUES(memory_alerts),
        database_alerts = VALUES(database_alerts),
        redis_alerts = VALUES(redis_alerts),
        system_alerts = VALUES(system_alerts),
        avg_memory_usage = VALUES(avg_memory_usage),
        max_memory_usage = VALUES(max_memory_usage),
        avg_db_response_time = VALUES(avg_db_response_time),
        max_db_response_time = VALUES(max_db_response_time),
        avg_redis_response_time = VALUES(avg_redis_response_time),
        max_redis_response_time = VALUES(max_redis_response_time),
        system_uptime_percentage = VALUES(system_uptime_percentage),
        update_time = CURRENT_TIMESTAMP;
        
END$$

DELIMITER ;

-- 8. 创建视图用于监控数据查询
CREATE OR REPLACE VIEW v_monitor_dashboard AS
SELECT 
    DATE(p.record_time) as monitor_date,
    HOUR(p.record_time) as monitor_hour,
    AVG(p.memory_usage_percentage) as avg_memory_usage,
    MAX(p.memory_usage_percentage) as max_memory_usage,
    AVG(p.db_response_time) as avg_db_response_time,
    MAX(p.db_response_time) as max_db_response_time,
    AVG(p.redis_response_time) as avg_redis_response_time,
    MAX(p.redis_response_time) as max_redis_response_time,
    SUM(CASE WHEN p.system_healthy = 1 THEN 1 ELSE 0 END) as healthy_count,
    COUNT(p.id) as total_count,
    (SUM(CASE WHEN p.system_healthy = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(p.id)) as uptime_percentage
FROM sys_monitor_performance p
WHERE p.record_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(p.record_time), HOUR(p.record_time)
ORDER BY monitor_date DESC, monitor_hour DESC;

-- 9. 创建告警统计视图
CREATE OR REPLACE VIEW v_alert_summary AS
SELECT 
    alert_type,
    alert_level,
    COUNT(*) as alert_count,
    COUNT(CASE WHEN alert_status = 'ACTIVE' THEN 1 END) as active_count,
    COUNT(CASE WHEN alert_status = 'RESOLVED' THEN 1 END) as resolved_count,
    MAX(alert_time) as last_alert_time,
    AVG(TIMESTAMPDIFF(MINUTE, alert_time, COALESCE(resolved_time, NOW()))) as avg_resolution_minutes
FROM sys_monitor_alert
WHERE alert_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY alert_type, alert_level
ORDER BY alert_count DESC;

-- 10. 创建索引优化查询性能
CREATE INDEX idx_performance_record_time_healthy ON sys_monitor_performance(record_time, system_healthy);
CREATE INDEX idx_alert_time_type_status ON sys_monitor_alert(alert_time, alert_type, alert_status);

-- =============================================
-- 验证脚本
-- =============================================

-- 查看创建的表
SHOW TABLES LIKE 'sys_monitor%';

-- 查看监控配置
SELECT * FROM sys_monitor_config ORDER BY config_group, config_key;

-- 查看视图
SHOW CREATE VIEW v_monitor_dashboard;
SHOW CREATE VIEW v_alert_summary;

COMMIT;
