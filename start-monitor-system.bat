@echo off
echo ========================================
echo    RuoYi-Vue 监控系统启动脚本
echo ========================================
echo.

:: 设置编码为UTF-8
chcp 65001 > nul

:: 检查Java环境
echo [1/5] 检查Java环境...
java -version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Java环境，请确保已安装JDK 8+
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

:: 检查Maven环境
echo [2/5] 检查Maven环境...
mvn -version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Maven环境，请确保已安装Maven
    pause
    exit /b 1
)
echo ✅ Maven环境检查通过

:: 检查项目文件
echo [3/5] 检查项目文件...
if not exist "ruoyi-admin\target\ruoyi-admin.jar" (
    echo ⚠️  未找到编译后的JAR文件，开始编译项目...
    echo 正在编译项目，请稍候...
    mvn clean package -DskipTests > compile.log 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 编译失败，请检查compile.log文件
        pause
        exit /b 1
    )
    echo ✅ 项目编译完成
) else (
    echo ✅ 项目文件检查通过
)

:: 检查端口占用
echo [4/5] 检查端口占用...
netstat -an | findstr :55557 > nul
if %errorlevel% equ 0 (
    echo ⚠️  端口55557已被占用，请关闭占用该端口的程序或修改配置
    echo 当前占用端口55557的进程：
    netstat -ano | findstr :55557
    pause
)

:: 启动应用
echo [5/5] 启动监控系统...
echo.
echo 🚀 正在启动RuoYi-Vue监控系统...
echo 📊 后端服务端口: 55557
echo 🌐 前端服务端口: 33334 (需要单独启动)
echo 📚 Swagger文档: http://localhost:55557/swagger-ui/index.html
echo 🔍 API测试工具: file:///%CD%/test/monitor-api-test.html
echo 📈 监控仪表板: file:///%CD%/frontend/monitor-dashboard.html
echo.
echo 启动参数：
echo - 配置文件: dev
echo - 数据库端口: 3308
echo - Redis密码: ankaixin.redis
echo.

:: 启动应用
java -jar ruoyi-admin\target\ruoyi-admin.jar ^
    --spring.profiles.active=dev ^
    --server.port=55557 ^
    --spring.datasource.druid.master.url=***************************************************************************************************************************************************** ^
    --spring.datasource.druid.master.username=root ^
    --spring.datasource.druid.master.password=ankaixin.docker.mysql ^
    --spring.redis.host=localhost ^
    --spring.redis.port=6379 ^
    --spring.redis.password=ankaixin.redis

echo.
echo 应用已退出
pause
