package com.ruoyi.framework.monitor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 监控告警管理器
 * 
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "ruoyi.monitor.enabled", havingValue = "true", matchIfMissing = true)
public class AlertManager
{
    private static final Logger log = LoggerFactory.getLogger(AlertManager.class);
    
    @Autowired
    private PerformanceMonitor performanceMonitor;
    
    @Autowired(required = false)
    private JavaMailSender mailSender;
    
    @Value("${ruoyi.monitor.alert.email.enabled:false}")
    private boolean emailAlertEnabled;
    
    @Value("${ruoyi.monitor.alert.email.to:<EMAIL>}")
    private String alertEmailTo;
    
    @Value("${ruoyi.monitor.alert.email.from:<EMAIL>}")
    private String alertEmailFrom;
    
    // 告警阈值配置
    @Value("${ruoyi.monitor.alert.memory.threshold:85}")
    private double memoryThreshold;
    
    @Value("${ruoyi.monitor.alert.response.threshold:5000}")
    private long responseTimeThreshold;
    
    @Value("${ruoyi.monitor.alert.error.threshold:10}")
    private int errorCountThreshold;
    
    // 告警频率控制（避免告警风暴）
    private final Map<String, LocalDateTime> lastAlertTime = new ConcurrentHashMap<>();
    private final Map<String, AtomicInteger> alertCounts = new ConcurrentHashMap<>();
    
    // 告警冷却时间（分钟）
    private static final int ALERT_COOLDOWN_MINUTES = 30;
    
    /**
     * 检查系统指标并触发告警
     */
    public void checkAndAlert()
    {
        try {
            Map<String, Object> metrics = performanceMonitor.getSystemMetrics();
            
            // 检查内存使用率
            checkMemoryUsage(metrics);
            
            // 检查数据库响应时间
            checkDatabaseResponseTime(metrics);
            
            // 检查Redis响应时间
            checkRedisResponseTime(metrics);
            
            // 检查系统健康状态
            checkSystemHealth();
            
        } catch (Exception e) {
            log.error("监控检查失败", e);
            sendAlert("MONITOR_ERROR", "监控系统异常", "监控检查过程中发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 检查内存使用率
     */
    private void checkMemoryUsage(Map<String, Object> metrics)
    {
        @SuppressWarnings("unchecked")
        Map<String, Object> memory = (Map<String, Object>) metrics.get("memory");
        if (memory != null) {
            Double heapUsedPercent = (Double) memory.get("heapUsedPercent");
            if (heapUsedPercent != null && heapUsedPercent > memoryThreshold) {
                String message = String.format("内存使用率过高: %.2f%% (阈值: %.2f%%)", 
                    heapUsedPercent, memoryThreshold);
                sendAlert("HIGH_MEMORY_USAGE", "内存使用率告警", message);
            }
        }
    }
    
    /**
     * 检查数据库响应时间
     */
    private void checkDatabaseResponseTime(Map<String, Object> metrics)
    {
        @SuppressWarnings("unchecked")
        Map<String, Object> database = (Map<String, Object>) metrics.get("database");
        if (database != null) {
            Object connectionTimeObj = database.get("connectionTime");
            if (connectionTimeObj instanceof Number) {
                long connectionTime = ((Number) connectionTimeObj).longValue();
                if (connectionTime > responseTimeThreshold) {
                    String message = String.format("数据库响应时间过长: %dms (阈值: %dms)", 
                        connectionTime, responseTimeThreshold);
                    sendAlert("SLOW_DATABASE", "数据库响应慢", message);
                }
            }
            
            // 检查数据库连接状态
            String status = (String) database.get("status");
            if (!"healthy".equals(status)) {
                String error = (String) database.get("error");
                String message = "数据库连接异常: " + (error != null ? error : "未知错误");
                sendAlert("DATABASE_ERROR", "数据库连接异常", message);
            }
        }
    }
    
    /**
     * 检查Redis响应时间
     */
    private void checkRedisResponseTime(Map<String, Object> metrics)
    {
        @SuppressWarnings("unchecked")
        Map<String, Object> redis = (Map<String, Object>) metrics.get("redis");
        if (redis != null) {
            Object responseTimeObj = redis.get("responseTime");
            if (responseTimeObj instanceof Number) {
                long responseTime = ((Number) responseTimeObj).longValue();
                if (responseTime > responseTimeThreshold) {
                    String message = String.format("Redis响应时间过长: %dms (阈值: %dms)", 
                        responseTime, responseTimeThreshold);
                    sendAlert("SLOW_REDIS", "Redis响应慢", message);
                }
            }
            
            // 检查Redis连接状态
            String status = (String) redis.get("status");
            if (!"healthy".equals(status)) {
                String error = (String) redis.get("error");
                String message = "Redis连接异常: " + (error != null ? error : "未知错误");
                sendAlert("REDIS_ERROR", "Redis连接异常", message);
            }
        }
    }
    
    /**
     * 检查系统整体健康状态
     */
    private void checkSystemHealth()
    {
        boolean isHealthy = performanceMonitor.isSystemHealthy();
        if (!isHealthy) {
            sendAlert("SYSTEM_UNHEALTHY", "系统健康检查失败", "系统健康检查未通过，请检查相关组件状态");
        }
    }
    
    /**
     * 发送告警
     */
    private void sendAlert(String alertType, String title, String message)
    {
        // 检查告警频率控制
        if (!shouldSendAlert(alertType)) {
            log.debug("告警被频率控制跳过: {}", alertType);
            return;
        }
        
        // 记录告警
        log.warn("系统告警 [{}]: {}", alertType, message);
        
        // 更新告警时间和计数
        lastAlertTime.put(alertType, LocalDateTime.now());
        alertCounts.computeIfAbsent(alertType, k -> new AtomicInteger(0)).incrementAndGet();
        
        // 发送邮件告警
        if (emailAlertEnabled && mailSender != null) {
            sendEmailAlert(alertType, title, message);
        }
        
        // 这里可以添加其他告警方式，如短信、钉钉、企业微信等
        // sendSmsAlert(title, message);
        // sendDingTalkAlert(title, message);
        // sendWeChatAlert(title, message);
    }
    
    /**
     * 检查是否应该发送告警（频率控制）
     */
    private boolean shouldSendAlert(String alertType)
    {
        LocalDateTime lastTime = lastAlertTime.get(alertType);
        if (lastTime == null) {
            return true;
        }
        
        LocalDateTime now = LocalDateTime.now();
        return now.isAfter(lastTime.plusMinutes(ALERT_COOLDOWN_MINUTES));
    }
    
    /**
     * 发送邮件告警
     */
    @Async
    private void sendEmailAlert(String alertType, String title, String message)
    {
        try {
            SimpleMailMessage mailMessage = new SimpleMailMessage();
            mailMessage.setFrom(alertEmailFrom);
            mailMessage.setTo(alertEmailTo);
            mailMessage.setSubject("[系统告警] " + title);
            
            StringBuilder body = new StringBuilder();
            body.append("告警类型: ").append(alertType).append("\n");
            body.append("告警时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            body.append("告警内容: ").append(message).append("\n\n");
            body.append("请及时处理相关问题。\n");
            body.append("系统监控");
            
            mailMessage.setText(body.toString());
            mailSender.send(mailMessage);
            
            log.info("告警邮件发送成功: {}", alertType);
        } catch (Exception e) {
            log.error("发送告警邮件失败: " + alertType, e);
        }
    }
    
    /**
     * 获取告警统计信息
     */
    public Map<String, Object> getAlertStatistics()
    {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("alertCounts", alertCounts);
        stats.put("lastAlertTimes", lastAlertTime);
        return stats;
    }
    
    /**
     * 重置告警统计
     */
    public void resetAlertStatistics()
    {
        alertCounts.clear();
        lastAlertTime.clear();
        log.info("告警统计信息已重置");
    }
    
    /**
     * 手动发送测试告警
     */
    public void sendTestAlert()
    {
        sendAlert("TEST_ALERT", "测试告警", "这是一条测试告警消息，用于验证告警系统是否正常工作。");
    }
    
    /**
     * 获取告警配置信息
     */
    public Map<String, Object> getAlertConfig()
    {
        Map<String, Object> config = new ConcurrentHashMap<>();
        config.put("emailAlertEnabled", emailAlertEnabled);
        config.put("alertEmailTo", alertEmailTo);
        config.put("alertEmailFrom", alertEmailFrom);
        config.put("memoryThreshold", memoryThreshold);
        config.put("responseTimeThreshold", responseTimeThreshold);
        config.put("errorCountThreshold", errorCountThreshold);
        config.put("alertCooldownMinutes", ALERT_COOLDOWN_MINUTES);
        return config;
    }
}
