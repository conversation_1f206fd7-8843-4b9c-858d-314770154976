# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: ***********************************************************************************************************************************************
                username: ${DB_USERNAME:root}
                password: ${DB_PASSWORD:123456}
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url: 
                username: 
                password: 
            # 初始连接数 - 提高初始连接数以减少启动时延迟
            initialSize: 10
            # 最小连接池数量 - 保持足够的空闲连接
            minIdle: 15
            # 最大连接池数量 - 提高最大连接数支持更高并发
            maxActive: 50
            # 配置获取连接等待超时的时间 - 减少等待时间提高响应速度
            maxWait: 10000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 - 更频繁的检测
            timeBetweenEvictionRunsMillis: 30000
            # 配置一个连接在池中最小生存的时间，单位是毫秒 - 适当减少以释放长时间空闲连接
            minEvictableIdleTimeMillis: 180000
            # 配置一个连接在池中最大生存的时间，单位是毫秒 - 防止连接过期
            maxEvictableIdleTimeMillis: 600000
            # 配置检测连接是否有效 - 使用更轻量的检测查询
            validationQuery: SELECT 1
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            # 连接池性能优化配置
            poolPreparedStatements: true
            maxPoolPreparedStatementPerConnectionSize: 20
            # 连接泄漏检测
            removeAbandoned: true
            removeAbandonedTimeout: 1800
            logAbandoned: true
            webStatFilter: 
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: ${DRUID_USERNAME:ruoyi}
                login-password: ${DRUID_PASSWORD:RuoYi@2025!}
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true