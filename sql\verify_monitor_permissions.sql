-- 验证监控权限是否正确添加

-- 1. 查看监控相关菜单
SELECT 
    menu_id,
    menu_name,
    parent_id,
    path,
    perms,
    menu_type,
    visible,
    status
FROM sys_menu 
WHERE menu_name LIKE '%监控%' OR perms LIKE 'monitor:%'
ORDER BY parent_id, order_num;

-- 2. 查看管理员角色的监控权限
SELECT 
    r.role_name,
    m.menu_name,
    m.perms,
    m.menu_type
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_key = 'admin' AND (m.menu_name LIKE '%监控%' OR m.perms LIKE 'monitor:%')
ORDER BY m.parent_id, m.order_num;

-- 3. 统计监控权限数量
SELECT 
    '监控菜单总数' as item,
    COUNT(*) as count
FROM sys_menu 
WHERE menu_name LIKE '%监控%' OR perms LIKE 'monitor:%'

UNION ALL

SELECT 
    '管理员监控权限数' as item,
    COUNT(*) as count
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_key = 'admin' AND (m.menu_name LIKE '%监控%' OR m.perms LIKE 'monitor:%');
