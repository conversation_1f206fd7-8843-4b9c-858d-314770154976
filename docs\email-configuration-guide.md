# 📧 邮件服务器配置指南

## 🎯 配置目标
为监控系统配置邮件告警功能，当系统出现异常时自动发送邮件通知。

## 📋 配置步骤

### 1. 选择邮件服务提供商

#### 🔸 QQ邮箱配置
```yaml
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-authorization-code  # QQ邮箱授权码，不是登录密码
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
```

**获取QQ邮箱授权码步骤：**
1. 登录QQ邮箱网页版
2. 点击"设置" → "账户"
3. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
4. 开启"POP3/SMTP服务"或"IMAP/SMTP服务"
5. 按提示发送短信，获取授权码
6. 将授权码填入password字段

#### 🔸 163邮箱配置
```yaml
spring:
  mail:
    host: smtp.163.com
    port: 25
    username: <EMAIL>
    password: your-authorization-code  # 163邮箱授权码
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
```

**获取163邮箱授权码步骤：**
1. 登录163邮箱网页版
2. 点击"设置" → "POP3/SMTP/IMAP"
3. 开启"POP3/SMTP服务"
4. 按提示设置授权码
5. 将授权码填入password字段

#### 🔸 Gmail配置
```yaml
spring:
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: your-app-password  # Gmail应用专用密码
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
```

#### 🔸 企业邮箱配置
```yaml
spring:
  mail:
    host: smtp.exmail.qq.com  # 企业微信邮箱
    port: 587
    username: <EMAIL>
    password: your-password
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
```

### 2. 更新配置文件

#### 2.1 修改application.yml
将以下配置替换到 `ruoyi-admin/src/main/resources/application.yml` 中：

```yaml
spring:
  mail:
    # 根据您的邮箱提供商选择对应配置
    host: smtp.qq.com  # 邮件服务器地址
    port: 587          # 邮件服务器端口
    username: <EMAIL>  # 您的实际邮箱地址
    password: your-actual-auth-code     # 您的实际授权码
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

ruoyi:
  monitor:
    alert:
      email:
        enabled: true  # 启用邮件告警
        to: <EMAIL>      # 接收告警的邮箱
        from: <EMAIL>   # 发送告警的邮箱（通常与username相同）
```

#### 2.2 配置示例（请替换为实际信息）
```yaml
# 示例：使用QQ邮箱
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: abcdefghijklmnop  # QQ邮箱授权码
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

ruoyi:
  monitor:
    alert:
      email:
        enabled: true
        to: <EMAIL>
        from: <EMAIL>
```

### 3. 测试邮件配置

#### 3.1 重启应用
```bash
# 停止当前应用
# 重新启动应用以加载新配置
java -jar ruoyi-admin.jar --spring.profiles.active=dev
```

#### 3.2 测试邮件发送
1. 登录系统管理界面
2. 访问监控管理页面
3. 点击"测试告警"按钮
4. 检查邮箱是否收到测试邮件

#### 3.3 使用API测试
```bash
# 使用curl测试邮件告警API
curl -X POST http://localhost:8080/monitor/alert/test \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 常见问题解决

#### 🔸 问题1：邮件发送失败
**可能原因：**
- 邮箱授权码错误
- 邮箱服务未开启SMTP
- 网络连接问题

**解决方案：**
1. 重新获取邮箱授权码
2. 确认SMTP服务已开启
3. 检查防火墙设置

#### 🔸 问题2：邮件被拒绝
**可能原因：**
- 发件人邮箱与配置不匹配
- 邮件内容被识别为垃圾邮件

**解决方案：**
1. 确保from字段与username一致
2. 修改邮件模板内容

#### 🔸 问题3：SSL连接错误
**可能原因：**
- SSL/TLS配置错误
- 端口配置错误

**解决方案：**
```yaml
spring:
  mail:
    properties:
      mail:
        smtp:
          ssl:
            enable: true  # 启用SSL
          socketFactory:
            port: 465
            class: javax.net.ssl.SSLSocketFactory
```

### 5. 邮件模板自定义

#### 5.1 告警邮件模板
系统默认的告警邮件包含：
- 告警类型和级别
- 告警时间
- 系统状态信息
- 详细的错误信息

#### 5.2 自定义邮件内容
如需自定义邮件模板，可以修改 `AlertManager.java` 中的邮件发送方法。

### 6. 安全建议

#### 🔸 授权码安全
- 不要将授权码提交到代码仓库
- 使用环境变量存储敏感信息
- 定期更换授权码

#### 🔸 环境变量配置
```yaml
spring:
  mail:
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-auth-code}

ruoyi:
  monitor:
    alert:
      email:
        to: ${ALERT_EMAIL_TO:<EMAIL>}
```

### 7. 验证配置完成

#### ✅ 配置检查清单
- [ ] 邮箱SMTP服务已开启
- [ ] 获取了正确的授权码
- [ ] application.yml配置已更新
- [ ] 邮件告警已启用（enabled: true）
- [ ] 应用已重启
- [ ] 测试邮件发送成功

#### 🎯 完成标志
当您能够通过"测试告警"功能成功收到邮件时，邮件配置就完成了。

---

## 📞 技术支持

如果在配置过程中遇到问题，请：
1. 检查应用日志中的错误信息
2. 验证邮箱服务商的SMTP设置
3. 确认网络连接正常
4. 参考邮箱服务商的官方文档

**配置完成后，您的监控系统将具备完整的邮件告警功能！** 📧
