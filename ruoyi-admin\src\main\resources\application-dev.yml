# 开发环境配置
ruoyi:
  # 文件路径 - 开发环境路径
  profile: ${UPLOAD_PATH:D:/ruoyi/uploadPath}

# APM监控配置
monitor:
  application-name: ruoyi-vue-dev
  instance-id: ${spring.application.name:ruoyi-vue}-${server.port:8080}
  environment: dev
  enabled: false


  retention-days: 7
  sampling-rate: 1.0
  alert-thresholds:
    cpu-usage-threshold: 80.0
    memory-usage-threshold: 85.0
    response-time-threshold: 1000
    error-rate-threshold: 5.0
  notification:
    webhook:
      enabled: false
      url: http://localhost:8080/webhook/alert
    email:
      enabled: false
    dingtalk:
      enabled: false
      webhook: https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN
    wechat:
      enabled: false
      webhook: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY

# 服务器配置
server:
  port: ${SERVER_PORT:55557}
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    accept-count: 1000
    threads:
      max: 800
      min-spare: 100

# 日志配置 - 开发环境详细日志
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# Spring配置
spring:
  # 应用名称
  application:
    name: ruoyi-vue
  # 开启热部署
  devtools:
    restart:
      enabled: true
  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    database: ${REDIS_DATABASE:0}
    password: ${REDIS_PASSWORD:ankaixin.redis}
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      master:
        url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3308}/${DB_NAME:ry-vue}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
        username: ${DB_USERNAME:root}
        password: ${DB_PASSWORD:ankaixin.docker.mysql}
      slave:
        enabled: false
      # 连接池配置 - 开发环境
      initialSize: ${DB_POOL_INITIAL_SIZE:10}
      minIdle: ${DB_POOL_MIN_IDLE:15}
      maxActive: ${DB_POOL_MAX_ACTIVE:50}
      maxWait: ${DB_POOL_MAX_WAIT:10000}
      connectTimeout: 30000
      socketTimeout: 60000
      timeBetweenEvictionRunsMillis: 30000
      minEvictableIdleTimeMillis: 180000
      maxEvictableIdleTimeMillis: 600000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      removeAbandoned: true
      removeAbandonedTimeout: 1800
      logAbandoned: true
      # 开发环境启用监控页面
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        allow:
        url-pattern: /druid/*
        login-username: ruoyi
        login-password: 123456
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

# Spring Boot Actuator配置
management:
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: "*"
      base-path: /actuator
  endpoint:
    health:
      enabled: true
      show-details: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
        step: 10s
        descriptions: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.9, 0.95, 0.99
    tags:
      application: ${spring.application.name}
      environment: ${monitor.environment}
    web:
      server:
        request:
          autotime:
            enabled: true

# SkyWalking配置
skywalking:
  enabled: true
  agent:
    service-name: ${spring.application.name}
    instance-name: ${spring.application.name}-${server.port}
    collector:
      backend-service: ${SKYWALKING_COLLECTOR:127.0.0.1:11800}
    sampling-rate: ${monitor.sampling-rate:1.0}
    trace:
      ignore-path: /actuator/**,/static/**,/css/**,/js/**,/images/**
    logging:
      level: WARN


