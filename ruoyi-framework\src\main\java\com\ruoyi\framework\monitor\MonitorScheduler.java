package com.ruoyi.framework.monitor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 监控定时任务
 * 
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "ruoyi.monitor.enabled", havingValue = "true", matchIfMissing = true)
public class MonitorScheduler {
    private static final Logger log = LoggerFactory.getLogger(MonitorScheduler.class);

    @Autowired
    private PerformanceMonitor performanceMonitor;

    @Autowired
    private AlertManager alertManager;

    /**
     * 每分钟执行一次系统监控检查
     */
    @Scheduled(fixedRate = 60000) // 60秒
    public void performSystemCheck() {
        try {
            log.debug("开始执行系统监控检查");
            alertManager.checkAndAlert();
            log.debug("系统监控检查完成");
        } catch (Exception e) {
            log.error("系统监控检查异常", e);
        }
    }

    /**
     * 每5分钟记录一次性能指标
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void recordPerformanceMetrics() {
        try {
            log.debug("开始记录性能指标");
            String report = performanceMonitor.getPerformanceReport();
            log.info("性能监控报告:\n{}", report);
        } catch (Exception e) {
            log.error("记录性能指标异常", e);
        }
    }

    /**
     * 每小时重置性能计数器
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void resetPerformanceCounters() {
        try {
            log.debug("重置性能计数器");
            performanceMonitor.resetCounters();
        } catch (Exception e) {
            log.error("重置性能计数器异常", e);
        }
    }

    /**
     * 每天凌晨2点重置告警统计
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void resetAlertStatistics() {
        try {
            log.info("重置告警统计信息");
            alertManager.resetAlertStatistics();
        } catch (Exception e) {
            log.error("重置告警统计异常", e);
        }
    }
}
