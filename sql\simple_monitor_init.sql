-- 简化的监控权限初始化脚本
-- 适用于快速测试

-- 检查数据库连接
SELECT 'Database connection successful' as status;

-- 查看现有菜单数量
SELECT COUNT(*) as existing_menus FROM sys_menu;

-- 添加监控管理主菜单（如果不存在）
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2000, '系统监控', 0, 6, 'monitor', NULL, '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', NOW(), '', NULL, '系统监控目录');

-- 添加性能监控菜单
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2001, '性能监控', 2000, 1, 'performance', 'monitor/performance/index', '', 1, 0, 'C', '0', '0', 'monitor:performance:view', 'dashboard', 'admin', NOW(), '', NULL, '性能监控菜单');

-- 添加健康检查菜单
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2002, '健康检查', 2000, 2, 'health', 'monitor/health/index', '', 1, 0, 'C', '0', '0', 'monitor:health:view', 'validCode', 'admin', NOW(), '', NULL, '系统健康检查菜单');

-- 添加告警管理菜单
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2003, '告警管理', 2000, 3, 'alert', 'monitor/alert/index', '', 1, 0, 'C', '0', '0', 'monitor:alert:view', 'message', 'admin', NOW(), '', NULL, '告警管理菜单');

-- 添加资源监控菜单
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2004, '资源监控', 2000, 4, 'resource', 'monitor/resource/index', '', 1, 0, 'C', '0', '0', 'monitor:resource:view', 'server', 'admin', NOW(), '', NULL, '资源监控菜单');

-- 添加实时监控菜单
INSERT IGNORE INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (2005, '实时监控', 2000, 5, 'realtime', 'monitor/realtime/index', '', 1, 0, 'C', '0', '0', 'monitor:realtime:view', 'online', 'admin', NOW(), '', NULL, '实时监控菜单');

-- 为管理员角色分配监控权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES (1, 2000);
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES (1, 2001);
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES (1, 2002);
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES (1, 2003);
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES (1, 2004);
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) VALUES (1, 2005);

-- 验证结果
SELECT 'Monitor menus added successfully' as result;
SELECT menu_name, perms FROM sys_menu WHERE menu_name LIKE '%监控%' OR perms LIKE 'monitor:%';

-- 检查管理员权限
SELECT COUNT(*) as admin_monitor_permissions 
FROM sys_role_menu rm 
JOIN sys_menu m ON rm.menu_id = m.menu_id 
WHERE rm.role_id = 1 AND (m.menu_name LIKE '%监控%' OR m.perms LIKE 'monitor:%');
