# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.9.0
  # 版权年份
  copyrightYear: 2025
  # 文件路径 - 支持环境变量配置，适应不同操作系统
  profile: ${UPLOAD_PATH:D:/ruoyi/uploadPath}
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math
  # 监控配置
  monitor:
    # 是否启用监控
    enabled: true
    # 告警配置
    alert:
      # 邮件告警
      email:
        enabled: false
        to: <EMAIL>
        from: <EMAIL>
      # 内存使用率阈值(%)
      memory:
        threshold: 85
      # 响应时间阈值(ms)
      response:
        threshold: 5000
      # 错误次数阈值
      error:
        threshold: 10

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 55557
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# API限流配置
rate-limit:
  # 是否启用限流
  enabled: ${RATE_LIMIT_ENABLED:true}
  # 默认每分钟请求次数
  default-requests-per-minute: ${RATE_LIMIT_DEFAULT:60}
  # 登录接口每分钟请求次数
  login-requests-per-minute: ${RATE_LIMIT_LOGIN:10}
  # API接口每分钟请求次数
  api-requests-per-minute: ${RATE_LIMIT_API:100}
  # 管理接口每分钟请求次数
  admin-requests-per-minute: ${RATE_LIMIT_ADMIN:200}

# 缓存策略配置
cache:
  strategy:
    # 默认缓存时间（分钟）
    default-ttl: ${CACHE_DEFAULT_TTL:15}
    # 是否启用缓存预热
    enable-warmup: ${CACHE_ENABLE_WARMUP:true}
    # 是否启用缓存统计
    enable-stats: ${CACHE_ENABLE_STATS:true}
    # 缓存键前缀
    key-prefix: ${CACHE_KEY_PREFIX:ruoyi:}
    # 最大缓存大小
    max-size: ${CACHE_MAX_SIZE:10000}
    # 缓存压缩阈值（字节）
    compression-threshold: ${CACHE_COMPRESSION_THRESHOLD:1024}

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 解决Spring Boot 2.6+与Swagger 3.0兼容性问题
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: ankaixin.redis
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

  # 邮件配置
  mail:
    # 邮件服务器地址
    host: smtp.qq.com
    # 邮件服务器端口
    port: 587
    # 发送者邮箱
    username: <EMAIL>
    # 发送者邮箱授权码
    password: your-auth-code
    # 默认编码
    default-encoding: UTF-8
    # 其他配置
    properties:
      mail:
        smtp:
          # 开启认证
          auth: true
          # 开启starttls，用于gmail
          starttls:
            enable: true
            required: true

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥 - 使用环境变量或默认安全密钥
  secret: ${JWT_SECRET:MGFmZWFkYWEtMWM3YS00Y2U3LTllY2QtMWQ1Zjg0MjYxOWE0YmMyYTlhNWEtNTNiZC00YWNlLTgwMmMtMWJmNjMwNGIzMWI3}
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# CORS跨域配置
cors:
  # 允许的来源域名（多个用逗号分隔）
  allowedOrigins: ${CORS_ALLOWED_ORIGINS:http://localhost:33334,http://127.0.0.1:33334,http://localhost:33333,http://127.0.0.1:33333}
  # 是否允许凭据
  allowCredentials: ${CORS_ALLOW_CREDENTIALS:true}
  # 预检请求缓存时间（秒）
  maxAge: ${CORS_MAX_AGE:1800}

# 工作流配置
workflow:
  # 是否启用工作流功能
  enabled: true
