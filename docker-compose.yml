version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0.28
    container_name: ruoyi-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ankaixin.docker.mysql
      MYSQL_DATABASE: ry-vue
      MYSQL_USER: ruoyi
      MYSQL_PASSWORD: ruoyi123
    ports:
      - "3308:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_general_ci
    networks:
      - ruoyi-network

  # Redis缓存
  redis:
    image: redis:6.2.6-alpine
    container_name: ruoyi-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ankaixin.redis
    networks:
      - ruoyi-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  ruoyi-network:
    driver: bridge
