# RuoYi-Vue 监控系统API测试脚本
# PowerShell版本

Write-Host "========================================" -ForegroundColor Green
Write-Host "    RuoYi-Vue 监控系统API测试" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

$baseUrl = "http://localhost:8080"

# 测试服务器连接
Write-Host "[1/12] 测试服务器连接..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/swagger-ui/index.html" -Method GET -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 服务器连接正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ 服务器连接失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试Swagger文档
Write-Host "[2/12] 测试Swagger文档..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/swagger-ui/index.html" -Method GET -TimeoutSec 5
    Write-Host "✅ Swagger文档可访问 (状态码: $($response.StatusCode))" -ForegroundColor Green
    Write-Host "📚 Swagger地址: $baseUrl/swagger-ui/index.html" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Swagger文档访问失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试验证码接口
Write-Host "[3/12] 测试验证码接口..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/captchaImage" -Method GET -TimeoutSec 5
    Write-Host "✅ 验证码接口正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "⚠️  验证码接口访问失败: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 测试监控API（无认证）
$monitorApis = @(
    @{Name="健康检查"; Url="/monitor/health"; Method="GET"},
    @{Name="性能监控"; Url="/monitor/performance"; Method="GET"},
    @{Name="性能报告"; Url="/monitor/performance/report"; Method="GET"},
    @{Name="告警统计"; Url="/monitor/alert/statistics"; Method="GET"},
    @{Name="告警配置"; Url="/monitor/alert/config"; Method="GET"},
    @{Name="资源监控"; Url="/monitor/resource"; Method="GET"},
    @{Name="实时监控"; Url="/monitor/realtime"; Method="GET"},
    @{Name="测试告警"; Url="/monitor/alert/test"; Method="POST"},
    @{Name="手动检查"; Url="/monitor/alert/check"; Method="POST"}
)

$testCount = 4
foreach ($api in $monitorApis) {
    $testCount++
    Write-Host "[$testCount/12] 测试$($api.Name)..." -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl$($api.Url)" -Method $api.Method -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($api.Name) 接口正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
        } else {
            Write-Host "⚠️  $($api.Name) 返回状态码: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        if ($statusCode -eq 401) {
            Write-Host "🔒 $($api.Name) 需要认证 (状态码: 401)" -ForegroundColor Cyan
        } elseif ($statusCode -eq 403) {
            Write-Host "🚫 $($api.Name) 权限不足 (状态码: 403)" -ForegroundColor Yellow
        } else {
            Write-Host "❌ $($api.Name) 访问失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "🎉 API测试完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📊 测试结果总结：" -ForegroundColor Cyan
Write-Host "- 服务器运行在端口 8080" -ForegroundColor White
Write-Host "- Swagger文档可正常访问" -ForegroundColor White
Write-Host "- 监控API需要登录认证" -ForegroundColor White
Write-Host ""
Write-Host "🔗 重要链接：" -ForegroundColor Cyan
Write-Host "- Swagger文档: $baseUrl/swagger-ui/index.html" -ForegroundColor White
Write-Host "- API测试工具: file:///$(Get-Location)/test/monitor-api-test.html" -ForegroundColor White
Write-Host "- 监控仪表板: file:///$(Get-Location)/frontend/monitor-dashboard.html" -ForegroundColor White
Write-Host ""
Write-Host "📝 下一步操作：" -ForegroundColor Cyan
Write-Host "1. 在浏览器中打开Swagger文档" -ForegroundColor White
Write-Host "2. 使用admin/admin123登录获取token" -ForegroundColor White
Write-Host "3. 在API测试工具中配置token测试监控接口" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Green

# 自动打开相关页面
Write-Host ""
Write-Host "🚀 正在打开相关页面..." -ForegroundColor Yellow
Start-Process "$baseUrl/swagger-ui/index.html"
Start-Process "file:///$(Get-Location)/test/monitor-api-test.html"
Start-Process "file:///$(Get-Location)/frontend/monitor-dashboard.html"

Write-Host "✅ 页面已在浏览器中打开！" -ForegroundColor Green
