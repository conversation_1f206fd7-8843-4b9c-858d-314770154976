@echo off
echo ========================================
echo    监控系统API快速测试脚本
echo ========================================
echo.

:: 设置编码为UTF-8
chcp 65001 > nul

set BASE_URL=http://localhost:55557

echo 🔍 测试监控系统API接口...
echo 服务器地址: %BASE_URL%
echo.

:: 测试健康检查
echo [1/11] 测试健康检查 - GET /monitor/health
curl -s -w "状态码: %%{http_code}\n" %BASE_URL%/monitor/health
echo.

:: 测试性能监控
echo [2/11] 测试性能监控 - GET /monitor/performance
curl -s -w "状态码: %%{http_code}\n" %BASE_URL%/monitor/performance
echo.

:: 测试性能报告
echo [3/11] 测试性能报告 - GET /monitor/performance/report
curl -s -w "状态码: %%{http_code}\n" %BASE_URL%/monitor/performance/report
echo.

:: 测试告警统计
echo [4/11] 测试告警统计 - GET /monitor/alert/statistics
curl -s -w "状态码: %%{http_code}\n" %BASE_URL%/monitor/alert/statistics
echo.

:: 测试告警配置
echo [5/11] 测试告警配置 - GET /monitor/alert/config
curl -s -w "状态码: %%{http_code}\n" %BASE_URL%/monitor/alert/config
echo.

:: 测试资源监控
echo [6/11] 测试资源监控 - GET /monitor/resource
curl -s -w "状态码: %%{http_code}\n" %BASE_URL%/monitor/resource
echo.

:: 测试实时监控
echo [7/11] 测试实时监控 - GET /monitor/realtime
curl -s -w "状态码: %%{http_code}\n" %BASE_URL%/monitor/realtime
echo.

:: 测试Swagger文档
echo [8/11] 测试Swagger文档 - GET /swagger-ui/index.html
curl -s -w "状态码: %%{http_code}\n" %BASE_URL%/swagger-ui/index.html > nul
echo.

:: 测试测试告警
echo [9/11] 测试告警发送 - POST /monitor/alert/test
curl -s -X POST -w "状态码: %%{http_code}\n" %BASE_URL%/monitor/alert/test
echo.

:: 测试手动检查
echo [10/11] 测试手动检查 - POST /monitor/alert/check
curl -s -X POST -w "状态码: %%{http_code}\n" %BASE_URL%/monitor/alert/check
echo.

:: 测试重置计数器
echo [11/11] 测试重置计数器 - DELETE /monitor/performance/counters
curl -s -X DELETE -w "状态码: %%{http_code}\n" %BASE_URL%/monitor/performance/counters
echo.

echo ========================================
echo 🎉 API测试完成！
echo.
echo 📚 打开Swagger文档: %BASE_URL%/swagger-ui/index.html
echo 🧪 打开API测试工具: file:///%CD%/test/monitor-api-test.html
echo 📈 打开监控仪表板: file:///%CD%/frontend/monitor-dashboard.html
echo.
echo 如果看到状态码200，说明API正常工作
echo 如果看到状态码401，说明需要登录认证
echo 如果看到状态码404，说明路径不存在
echo 如果看到状态码500，说明服务器内部错误
echo ========================================

pause
