-- =============================================
-- 监控系统权限初始化脚本
-- 创建时间：2025-01-31
-- 说明：为监控系统添加菜单和权限配置
-- =============================================

-- 1. 添加监控管理主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('系统监控', 2, 5, 'monitor', NULL, '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', sysdate(), '', NULL, '系统监控目录');

-- 获取刚插入的监控主菜单ID
SET @monitor_menu_id = LAST_INSERT_ID();

-- 2. 添加性能监控菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('性能监控', @monitor_menu_id, 1, 'performance', 'monitor/performance/index', '', 1, 0, 'C', '0', '0', 'monitor:performance:view', 'dashboard', 'admin', sysdate(), '', NULL, '性能监控菜单');

-- 获取性能监控菜单ID
SET @performance_menu_id = LAST_INSERT_ID();

-- 3. 添加系统健康检查菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('健康检查', @monitor_menu_id, 2, 'health', 'monitor/health/index', '', 1, 0, 'C', '0', '0', 'monitor:health:view', 'validCode', 'admin', sysdate(), '', NULL, '系统健康检查菜单');

-- 获取健康检查菜单ID
SET @health_menu_id = LAST_INSERT_ID();

-- 4. 添加告警管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('告警管理', @monitor_menu_id, 3, 'alert', 'monitor/alert/index', '', 1, 0, 'C', '0', '0', 'monitor:alert:view', 'message', 'admin', sysdate(), '', NULL, '告警管理菜单');

-- 获取告警管理菜单ID
SET @alert_menu_id = LAST_INSERT_ID();

-- 5. 添加资源监控菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('资源监控', @monitor_menu_id, 4, 'resource', 'monitor/resource/index', '', 1, 0, 'C', '0', '0', 'monitor:resource:view', 'server', 'admin', sysdate(), '', NULL, '资源监控菜单');

-- 获取资源监控菜单ID
SET @resource_menu_id = LAST_INSERT_ID();

-- 6. 添加实时监控菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('实时监控', @monitor_menu_id, 5, 'realtime', 'monitor/realtime/index', '', 1, 0, 'C', '0', '0', 'monitor:realtime:view', 'online', 'admin', sysdate(), '', NULL, '实时监控菜单');

-- 获取实时监控菜单ID
SET @realtime_menu_id = LAST_INSERT_ID();

-- 7. 添加性能监控相关按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('性能报告', @performance_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'monitor:performance:report', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('重置计数器', @performance_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'monitor:performance:reset', '#', 'admin', sysdate(), '', NULL, '');

-- 8. 添加告警管理相关按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('告警配置', @alert_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'monitor:alert:config', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('重置统计', @alert_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'monitor:alert:reset', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('测试告警', @alert_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'monitor:alert:test', '#', 'admin', sysdate(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('手动检查', @alert_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'monitor:alert:check', '#', 'admin', sysdate(), '', NULL, '');

-- 9. 为管理员角色分配监控权限
-- 获取管理员角色ID（通常是1）
SET @admin_role_id = 1;

-- 获取所有新添加的菜单ID
SELECT menu_id INTO @monitor_main_id FROM sys_menu WHERE menu_name = '系统监控' AND parent_id = 2 ORDER BY menu_id DESC LIMIT 1;
SELECT menu_id INTO @performance_id FROM sys_menu WHERE menu_name = '性能监控' AND parent_id = @monitor_main_id ORDER BY menu_id DESC LIMIT 1;
SELECT menu_id INTO @health_id FROM sys_menu WHERE menu_name = '健康检查' AND parent_id = @monitor_main_id ORDER BY menu_id DESC LIMIT 1;
SELECT menu_id INTO @alert_id FROM sys_menu WHERE menu_name = '告警管理' AND parent_id = @monitor_main_id ORDER BY menu_id DESC LIMIT 1;
SELECT menu_id INTO @resource_id FROM sys_menu WHERE menu_name = '资源监控' AND parent_id = @monitor_main_id ORDER BY menu_id DESC LIMIT 1;
SELECT menu_id INTO @realtime_id FROM sys_menu WHERE menu_name = '实时监控' AND parent_id = @monitor_main_id ORDER BY menu_id DESC LIMIT 1;

-- 为管理员角色分配菜单权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (@admin_role_id, @monitor_main_id);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (@admin_role_id, @performance_id);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (@admin_role_id, @health_id);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (@admin_role_id, @alert_id);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (@admin_role_id, @resource_id);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (@admin_role_id, @realtime_id);

-- 为管理员角色分配按钮权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT @admin_role_id, menu_id FROM sys_menu 
WHERE perms IN (
    'monitor:performance:report',
    'monitor:performance:reset',
    'monitor:alert:config',
    'monitor:alert:reset',
    'monitor:alert:test',
    'monitor:alert:check'
);

-- 10. 创建监控操作员角色（可选）
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark)
VALUES ('监控操作员', 'monitor', 4, '1', 1, 1, '0', '0', 'admin', sysdate(), '系统监控操作员角色');

-- 获取监控操作员角色ID
SET @monitor_role_id = LAST_INSERT_ID();

-- 为监控操作员分配基础查看权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (@monitor_role_id, @monitor_main_id);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (@monitor_role_id, @performance_id);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (@monitor_role_id, @health_id);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (@monitor_role_id, @alert_id);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (@monitor_role_id, @resource_id);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (@monitor_role_id, @realtime_id);

-- 为监控操作员分配部分操作权限（不包括重置权限）
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT @monitor_role_id, menu_id FROM sys_menu 
WHERE perms IN (
    'monitor:performance:report',
    'monitor:alert:config',
    'monitor:alert:test',
    'monitor:alert:check'
);

-- =============================================
-- 验证脚本
-- =============================================

-- 查看新添加的监控菜单
SELECT 
    m.menu_id,
    m.menu_name,
    m.parent_id,
    m.path,
    m.perms,
    m.menu_type,
    p.menu_name as parent_name
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.menu_name LIKE '%监控%' OR m.perms LIKE 'monitor:%'
ORDER BY m.parent_id, m.order_num;

-- 查看管理员角色的监控权限
SELECT 
    r.role_name,
    m.menu_name,
    m.perms,
    m.menu_type
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_key = 'admin' AND (m.menu_name LIKE '%监控%' OR m.perms LIKE 'monitor:%')
ORDER BY m.parent_id, m.order_num;

COMMIT;
