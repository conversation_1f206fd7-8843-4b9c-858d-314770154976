package com.ruoyi.web.controller.monitor;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.framework.monitor.AlertManager;
import com.ruoyi.framework.monitor.PerformanceMonitor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 系统监控控制器
 * 提供完整的系统监控功能，包括性能监控、告警管理、资源监控等
 * 
 * <AUTHOR>
 */
@Api(tags = "系统监控")
@RestController
@RequestMapping("/monitor")
public class MonitorController extends BaseController
{
    @Autowired
    private PerformanceMonitor performanceMonitor;
    
    @Autowired
    private AlertManager alertManager;
    
    /**
     * 获取系统性能指标
     * 包括内存使用情况、数据库连接状态、Redis状态等
     */
    @ApiOperation("获取系统性能指标")
    @PreAuthorize("@ss.hasPermi('monitor:performance:view')")
    @GetMapping("/performance")
    public AjaxResult getPerformanceMetrics()
    {
        Map<String, Object> metrics = performanceMonitor.getSystemMetrics();
        return AjaxResult.success(metrics);
    }
    
    /**
     * 获取详细的性能报告
     * 包含格式化的性能统计信息
     */
    @ApiOperation("获取性能报告")
    @PreAuthorize("@ss.hasPermi('monitor:performance:view')")
    @GetMapping("/performance/report")
    public AjaxResult getPerformanceReport()
    {
        String report = performanceMonitor.getPerformanceReport();
        return AjaxResult.success("report", report);
    }
    
    /**
     * 检查系统整体健康状态
     * 综合评估各组件状态
     */
    @ApiOperation("检查系统健康状态")
    @PreAuthorize("@ss.hasPermi('monitor:health:view')")
    @GetMapping("/health")
    public AjaxResult checkSystemHealth()
    {
        boolean isHealthy = performanceMonitor.isSystemHealthy();
        Map<String, Object> metrics = performanceMonitor.getSystemMetrics();
        
        return AjaxResult.success()
                .put("healthy", isHealthy)
                .put("metrics", metrics)
                .put("timestamp", System.currentTimeMillis());
    }
    
    /**
     * 重置性能计数器
     * 清空所有性能统计数据
     */
    @ApiOperation("重置性能计数器")
    @PreAuthorize("@ss.hasPermi('monitor:performance:reset')")
    @Log(title = "性能监控", businessType = BusinessType.CLEAN)
    @DeleteMapping("/performance/counters")
    public AjaxResult resetPerformanceCounters()
    {
        performanceMonitor.resetCounters();
        return AjaxResult.success("性能计数器已重置");
    }
    
    /**
     * 获取告警统计信息
     * 包括告警次数、最后告警时间等
     */
    @ApiOperation("获取告警统计信息")
    @PreAuthorize("@ss.hasPermi('monitor:alert:view')")
    @GetMapping("/alert/statistics")
    public AjaxResult getAlertStatistics()
    {
        Map<String, Object> stats = alertManager.getAlertStatistics();
        return AjaxResult.success(stats);
    }
    
    /**
     * 获取告警配置信息
     * 包括阈值设置、邮件配置等
     */
    @ApiOperation("获取告警配置信息")
    @PreAuthorize("@ss.hasPermi('monitor:alert:view')")
    @GetMapping("/alert/config")
    public AjaxResult getAlertConfig()
    {
        Map<String, Object> config = alertManager.getAlertConfig();
        return AjaxResult.success(config);
    }
    
    /**
     * 重置告警统计数据
     * 清空告警历史记录
     */
    @ApiOperation("重置告警统计")
    @PreAuthorize("@ss.hasPermi('monitor:alert:reset')")
    @Log(title = "告警监控", businessType = BusinessType.CLEAN)
    @DeleteMapping("/alert/statistics")
    public AjaxResult resetAlertStatistics()
    {
        alertManager.resetAlertStatistics();
        return AjaxResult.success("告警统计已重置");
    }
    
    /**
     * 发送测试告警
     * 用于验证告警系统是否正常工作
     */
    @ApiOperation("发送测试告警")
    @PreAuthorize("@ss.hasPermi('monitor:alert:test')")
    @Log(title = "告警监控", businessType = BusinessType.OTHER)
    @PostMapping("/alert/test")
    public AjaxResult sendTestAlert()
    {
        alertManager.sendTestAlert();
        return AjaxResult.success("测试告警已发送");
    }
    
    /**
     * 手动触发监控检查
     * 立即执行一次完整的系统监控检查
     */
    @ApiOperation("手动触发监控检查")
    @PreAuthorize("@ss.hasPermi('monitor:alert:check')")
    @Log(title = "系统监控", businessType = BusinessType.OTHER)
    @PostMapping("/alert/check")
    public AjaxResult triggerMonitorCheck()
    {
        alertManager.checkAndAlert();
        return AjaxResult.success("监控检查已触发");
    }
    
    /**
     * 获取系统资源使用情况
     * 提供内存、数据库、Redis等关键资源的使用状态
     */
    @ApiOperation("获取系统资源使用情况")
    @PreAuthorize("@ss.hasPermi('monitor:resource:view')")
    @GetMapping("/resource")
    public AjaxResult getResourceUsage()
    {
        Map<String, Object> metrics = performanceMonitor.getSystemMetrics();
        
        // 提取关键资源信息
        @SuppressWarnings("unchecked")
        Map<String, Object> memory = (Map<String, Object>) metrics.get("memory");
        @SuppressWarnings("unchecked")
        Map<String, Object> database = (Map<String, Object>) metrics.get("database");
        @SuppressWarnings("unchecked")
        Map<String, Object> redis = (Map<String, Object>) metrics.get("redis");
        
        return AjaxResult.success()
                .put("memory", memory)
                .put("database", database)
                .put("redis", redis)
                .put("timestamp", System.currentTimeMillis());
    }
    
    /**
     * 获取实时监控数据
     * 提供用于前端实时图表的数据
     */
    @ApiOperation("获取实时监控数据")
    @PreAuthorize("@ss.hasPermi('monitor:realtime:view')")
    @GetMapping("/realtime")
    public AjaxResult getRealtimeData()
    {
        Map<String, Object> metrics = performanceMonitor.getSystemMetrics();
        
        // 构建实时数据响应
        return AjaxResult.success()
                .put("metrics", metrics)
                .put("healthy", performanceMonitor.isSystemHealthy())
                .put("alertStats", alertManager.getAlertStatistics())
                .put("timestamp", System.currentTimeMillis());
    }
}
