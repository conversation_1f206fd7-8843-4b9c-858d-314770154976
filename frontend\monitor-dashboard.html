<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控仪表板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .status-healthy { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 15px;
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #229954; }
        
        .btn-warning { background: #f39c12; }
        .btn-warning:hover { background: #e67e22; }
        
        .btn-danger { background: #e74c3c; }
        .btn-danger:hover { background: #c0392b; }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }
        
        .refresh-time {
            text-align: right;
            color: #7f8c8d;
            font-size: 12px;
            margin-top: 10px;
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 0 10px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 系统监控仪表板</h1>
        <p>实时监控系统性能和健康状态</p>
    </div>
    
    <div class="container">
        <!-- 控制面板 -->
        <div class="controls">
            <button class="btn" onclick="refreshData()">🔄 刷新数据</button>
            <button class="btn btn-success" onclick="testAlert()">🧪 测试告警</button>
            <button class="btn btn-warning" onclick="triggerCheck()">🔍 手动检查</button>
            <button class="btn btn-danger" onclick="resetCounters()">🗑️ 重置计数器</button>
            <label>
                <input type="checkbox" id="autoRefresh" checked> 自动刷新 (30秒)
            </label>
        </div>
        
        <!-- 系统状态概览 -->
        <div class="dashboard-grid">
            <div class="card">
                <div class="card-title">
                    <span class="status-indicator" id="systemStatus"></span>
                    系统健康状态
                </div>
                <div class="metric-value" id="healthStatus">检查中...</div>
                <div class="metric-label">整体系统状态</div>
                <div class="refresh-time" id="healthRefreshTime"></div>
            </div>
            
            <div class="card">
                <div class="card-title">💾 内存使用率</div>
                <div class="metric-value" id="memoryUsage">--%</div>
                <div class="metric-label">堆内存使用情况</div>
                <div class="chart-container">
                    <canvas id="memoryChart"></canvas>
                </div>
            </div>
            
            <div class="card">
                <div class="card-title">🗄️ 数据库状态</div>
                <div class="metric-value" id="dbStatus">检查中...</div>
                <div class="metric-label" id="dbResponseTime">响应时间: --ms</div>
                <div class="refresh-time" id="dbRefreshTime"></div>
            </div>
            
            <div class="card">
                <div class="card-title">🔴 Redis状态</div>
                <div class="metric-value" id="redisStatus">检查中...</div>
                <div class="metric-label" id="redisResponseTime">响应时间: --ms</div>
                <div class="refresh-time" id="redisRefreshTime"></div>
            </div>
        </div>
        
        <!-- 告警信息 -->
        <div class="card">
            <div class="card-title">🚨 告警统计</div>
            <div id="alertStats" class="loading">加载中...</div>
            <div class="chart-container">
                <canvas id="alertChart"></canvas>
            </div>
        </div>
        
        <!-- 性能图表 -->
        <div class="card">
            <div class="card-title">📈 性能趋势</div>
            <div class="chart-container">
                <canvas id="performanceChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        // 配置
        const API_BASE = 'http://localhost:8080';
        let charts = {};
        let refreshInterval;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            refreshData();
            startAutoRefresh();
        });
        
        // 初始化图表
        function initCharts() {
            // 内存使用率图表
            const memoryCtx = document.getElementById('memoryChart').getContext('2d');
            charts.memory = new Chart(memoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['已使用', '可用'],
                    datasets: [{
                        data: [0, 100],
                        backgroundColor: ['#e74c3c', '#ecf0f1'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            
            // 告警统计图表
            const alertCtx = document.getElementById('alertChart').getContext('2d');
            charts.alert = new Chart(alertCtx, {
                type: 'bar',
                data: {
                    labels: ['内存告警', '数据库告警', 'Redis告警', '其他告警'],
                    datasets: [{
                        label: '告警次数',
                        data: [0, 0, 0, 0],
                        backgroundColor: ['#e74c3c', '#f39c12', '#3498db', '#9b59b6']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // 性能趋势图表
            const performanceCtx = document.getElementById('performanceChart').getContext('2d');
            charts.performance = new Chart(performanceCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '内存使用率(%)',
                        data: [],
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        tension: 0.4
                    }, {
                        label: '数据库响应时间(ms)',
                        data: [],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            max: 100
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            }
                        }
                    }
                }
            });
        }
        
        // 刷新数据
        async function refreshData() {
            try {
                await Promise.all([
                    updateHealthStatus(),
                    updatePerformanceMetrics(),
                    updateAlertStats()
                ]);
            } catch (error) {
                console.error('刷新数据失败:', error);
                showAlert('数据刷新失败: ' + error.message, 'error');
            }
        }
        
        // 更新健康状态
        async function updateHealthStatus() {
            try {
                const response = await fetch(`${API_BASE}/monitor/health`);
                const data = await response.json();
                
                if (data.code === 200) {
                    const isHealthy = data.data.healthy;
                    const statusElement = document.getElementById('systemStatus');
                    const healthElement = document.getElementById('healthStatus');
                    
                    if (isHealthy) {
                        statusElement.className = 'status-indicator status-healthy';
                        healthElement.textContent = '健康';
                        healthElement.style.color = '#27ae60';
                    } else {
                        statusElement.className = 'status-indicator status-error';
                        healthElement.textContent = '异常';
                        healthElement.style.color = '#e74c3c';
                    }
                    
                    document.getElementById('healthRefreshTime').textContent = 
                        '更新时间: ' + new Date().toLocaleTimeString();
                }
            } catch (error) {
                console.error('健康检查失败:', error);
            }
        }
        
        // 更新性能指标
        async function updatePerformanceMetrics() {
            try {
                const response = await fetch(`${API_BASE}/monitor/performance`);
                const data = await response.json();
                
                if (data.code === 200) {
                    const metrics = data.data;
                    
                    // 更新内存使用率
                    if (metrics.memory) {
                        const memoryUsage = metrics.memory.usagePercentage || 0;
                        document.getElementById('memoryUsage').textContent = memoryUsage.toFixed(1) + '%';
                        
                        // 更新内存图表
                        charts.memory.data.datasets[0].data = [memoryUsage, 100 - memoryUsage];
                        charts.memory.update();
                        
                        // 更新性能趋势图表
                        const now = new Date().toLocaleTimeString();
                        if (charts.performance.data.labels.length > 10) {
                            charts.performance.data.labels.shift();
                            charts.performance.data.datasets[0].data.shift();
                            charts.performance.data.datasets[1].data.shift();
                        }
                        charts.performance.data.labels.push(now);
                        charts.performance.data.datasets[0].data.push(memoryUsage);
                    }
                    
                    // 更新数据库状态
                    if (metrics.database) {
                        const dbHealthy = metrics.database.healthy;
                        const dbResponseTime = metrics.database.responseTime || 0;
                        
                        document.getElementById('dbStatus').textContent = dbHealthy ? '正常' : '异常';
                        document.getElementById('dbStatus').style.color = dbHealthy ? '#27ae60' : '#e74c3c';
                        document.getElementById('dbResponseTime').textContent = `响应时间: ${dbResponseTime}ms`;
                        document.getElementById('dbRefreshTime').textContent = 
                            '更新时间: ' + new Date().toLocaleTimeString();
                        
                        // 更新性能趋势图表
                        if (charts.performance.data.datasets[1].data.length > 0) {
                            charts.performance.data.datasets[1].data[charts.performance.data.datasets[1].data.length - 1] = dbResponseTime;
                        } else {
                            charts.performance.data.datasets[1].data.push(dbResponseTime);
                        }
                    }
                    
                    // 更新Redis状态
                    if (metrics.redis) {
                        const redisHealthy = metrics.redis.healthy;
                        const redisResponseTime = metrics.redis.responseTime || 0;
                        
                        document.getElementById('redisStatus').textContent = redisHealthy ? '正常' : '异常';
                        document.getElementById('redisStatus').style.color = redisHealthy ? '#27ae60' : '#e74c3c';
                        document.getElementById('redisResponseTime').textContent = `响应时间: ${redisResponseTime}ms`;
                        document.getElementById('redisRefreshTime').textContent = 
                            '更新时间: ' + new Date().toLocaleTimeString();
                    }
                    
                    charts.performance.update();
                }
            } catch (error) {
                console.error('性能指标更新失败:', error);
            }
        }
        
        // 更新告警统计
        async function updateAlertStats() {
            try {
                const response = await fetch(`${API_BASE}/monitor/alert/statistics`);
                const data = await response.json();
                
                if (data.code === 200) {
                    const stats = data.data;
                    const alertStatsDiv = document.getElementById('alertStats');
                    
                    let html = '<div class="dashboard-grid">';
                    html += `<div><strong>总告警次数:</strong> ${stats.totalAlerts || 0}</div>`;
                    html += `<div><strong>最后告警时间:</strong> ${stats.lastAlertTime || '无'}</div>`;
                    html += `<div><strong>内存告警:</strong> ${stats.memoryAlerts || 0}</div>`;
                    html += `<div><strong>数据库告警:</strong> ${stats.databaseAlerts || 0}</div>`;
                    html += '</div>';
                    
                    alertStatsDiv.innerHTML = html;
                    
                    // 更新告警图表
                    charts.alert.data.datasets[0].data = [
                        stats.memoryAlerts || 0,
                        stats.databaseAlerts || 0,
                        stats.redisAlerts || 0,
                        stats.otherAlerts || 0
                    ];
                    charts.alert.update();
                }
            } catch (error) {
                console.error('告警统计更新失败:', error);
            }
        }
        
        // 测试告警
        async function testAlert() {
            try {
                const response = await fetch(`${API_BASE}/monitor/alert/test`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.code === 200) {
                    showAlert('测试告警已发送', 'success');
                } else {
                    showAlert('测试告警发送失败: ' + data.msg, 'error');
                }
            } catch (error) {
                showAlert('测试告警失败: ' + error.message, 'error');
            }
        }
        
        // 手动触发检查
        async function triggerCheck() {
            try {
                const response = await fetch(`${API_BASE}/monitor/alert/check`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.code === 200) {
                    showAlert('监控检查已触发', 'success');
                    setTimeout(refreshData, 2000); // 2秒后刷新数据
                } else {
                    showAlert('监控检查触发失败: ' + data.msg, 'error');
                }
            } catch (error) {
                showAlert('监控检查失败: ' + error.message, 'error');
            }
        }
        
        // 重置计数器
        async function resetCounters() {
            if (!confirm('确定要重置性能计数器吗？')) return;
            
            try {
                const response = await fetch(`${API_BASE}/monitor/performance/counters`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                
                if (data.code === 200) {
                    showAlert('性能计数器已重置', 'success');
                    setTimeout(refreshData, 1000); // 1秒后刷新数据
                } else {
                    showAlert('重置失败: ' + data.msg, 'error');
                }
            } catch (error) {
                showAlert('重置失败: ' + error.message, 'error');
            }
        }
        
        // 显示提示信息
        function showAlert(message, type = 'success') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.controls').nextSibling);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        // 自动刷新
        function startAutoRefresh() {
            const checkbox = document.getElementById('autoRefresh');
            
            function updateRefresh() {
                if (refreshInterval) {
                    clearInterval(refreshInterval);
                }
                
                if (checkbox.checked) {
                    refreshInterval = setInterval(refreshData, 30000); // 30秒
                }
            }
            
            checkbox.addEventListener('change', updateRefresh);
            updateRefresh();
        }
    </script>
</body>
</html>
