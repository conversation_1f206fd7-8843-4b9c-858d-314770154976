# 监控系统使用指南

## 📊 系统概述

RuoYi-Vue监控系统提供了全面的系统性能监控、健康检查、告警管理等功能，帮助管理员实时了解系统运行状态。

## 🚀 快速开始

### 1. 权限配置

执行权限初始化脚本：
```sql
-- 在数据库中执行
source sql/monitor_permissions.sql;
```

### 2. 邮件配置（可选）

在 `application.yml` 中配置邮件服务器：
```yaml
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-auth-code
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

ruoyi:
  monitor:
    alert:
      email:
        enabled: true
        to: <EMAIL>
        from: <EMAIL>
```

### 3. 监控配置

调整监控阈值（可选）：
```yaml
ruoyi:
  monitor:
    enabled: true
    alert:
      memory:
        threshold: 85  # 内存使用率阈值(%)
      response:
        threshold: 5000  # 响应时间阈值(ms)
      error:
        threshold: 10  # 错误次数阈值
```

## 📋 功能模块

### 1. 性能监控 (`/monitor/performance`)

**功能说明：**
- 实时监控系统性能指标
- 查看内存使用情况
- 监控数据库连接状态
- 查看Redis连接状态

**API接口：**
- `GET /monitor/performance` - 获取性能指标
- `GET /monitor/performance/report` - 获取详细报告
- `DELETE /monitor/performance/counters` - 重置计数器

**权限要求：**
- `monitor:performance:view` - 查看权限
- `monitor:performance:reset` - 重置权限

### 2. 健康检查 (`/monitor/health`)

**功能说明：**
- 综合评估系统健康状态
- 检查各组件连接状态
- 提供健康状态报告

**API接口：**
- `GET /monitor/health` - 系统健康检查

**权限要求：**
- `monitor:health:view` - 查看权限

### 3. 告警管理 (`/monitor/alert`)

**功能说明：**
- 查看告警统计信息
- 管理告警配置
- 发送测试告警
- 手动触发检查

**API接口：**
- `GET /monitor/alert/statistics` - 告警统计
- `GET /monitor/alert/config` - 告警配置
- `DELETE /monitor/alert/statistics` - 重置统计
- `POST /monitor/alert/test` - 测试告警
- `POST /monitor/alert/check` - 手动检查

**权限要求：**
- `monitor:alert:view` - 查看权限
- `monitor:alert:reset` - 重置权限
- `monitor:alert:test` - 测试权限
- `monitor:alert:check` - 检查权限

### 4. 资源监控 (`/monitor/resource`)

**功能说明：**
- 监控系统资源使用情况
- 查看内存、CPU、磁盘状态
- 监控数据库和Redis资源

**API接口：**
- `GET /monitor/resource` - 资源使用情况

**权限要求：**
- `monitor:resource:view` - 查看权限

### 5. 实时监控 (`/monitor/realtime`)

**功能说明：**
- 提供实时监控数据
- 支持前端图表展示
- 实时更新系统状态

**API接口：**
- `GET /monitor/realtime` - 实时监控数据

**权限要求：**
- `monitor:realtime:view` - 查看权限

## ⚠️ 告警机制

### 告警类型

1. **内存使用率告警**
   - 触发条件：内存使用率 > 85%
   - 告警级别：WARNING

2. **数据库响应慢告警**
   - 触发条件：连接时间 > 5000ms
   - 告警级别：WARNING

3. **Redis响应慢告警**
   - 触发条件：响应时间 > 5000ms
   - 告警级别：WARNING

4. **数据库连接异常**
   - 触发条件：数据库连接失败
   - 告警级别：ERROR

5. **Redis连接异常**
   - 触发条件：Redis连接失败
   - 告警级别：ERROR

6. **系统健康检查失败**
   - 触发条件：整体健康检查未通过
   - 告警级别：ERROR

### 告警频率控制

- **冷却时间**：30分钟
- **防止告警风暴**：相同类型告警在冷却时间内只发送一次
- **告警统计**：记录告警次数和最后告警时间

### 告警通知方式

1. **日志记录**：所有告警都会记录到系统日志
2. **邮件通知**：配置邮件服务器后可发送邮件告警
3. **扩展接口**：支持集成短信、钉钉、企业微信等

## 🔧 配置说明

### 监控开关

```yaml
ruoyi:
  monitor:
    enabled: true  # 启用/禁用监控系统
```

### 告警阈值配置

```yaml
ruoyi:
  monitor:
    alert:
      memory:
        threshold: 85  # 内存使用率阈值(%)
      response:
        threshold: 5000  # 响应时间阈值(ms)
      error:
        threshold: 10  # 错误次数阈值
```

### 邮件告警配置

```yaml
ruoyi:
  monitor:
    alert:
      email:
        enabled: true  # 启用邮件告警
        to: <EMAIL>  # 收件人
        from: <EMAIL>  # 发件人
```

## 📈 监控指标

### 系统指标

- **内存使用情况**
  - 堆内存使用量和使用率
  - 非堆内存使用量
  - 内存使用趋势

- **数据库指标**
  - 连接响应时间
  - 活跃连接数
  - 连接池状态
  - 连接健康状态

- **Redis指标**
  - 响应时间
  - 连接状态
  - 操作统计

### 性能计数器

- **方法调用次数**：记录关键方法的调用频率
- **执行时间统计**：记录方法执行耗时
- **错误统计**：记录系统错误次数

## 🛠️ 故障排查

### 常见问题

1. **监控数据不更新**
   - 检查监控开关是否启用
   - 查看定时任务是否正常运行
   - 检查日志是否有异常

2. **告警邮件发送失败**
   - 检查邮件服务器配置
   - 验证邮箱账号和授权码
   - 查看网络连接状态

3. **性能指标异常**
   - 检查数据库连接
   - 验证Redis连接
   - 查看系统资源使用情况

### 日志查看

```bash
# 查看监控相关日志
tail -f logs/ruoyi-admin.log | grep -i monitor

# 查看告警日志
tail -f logs/ruoyi-admin.log | grep -i alert

# 查看性能日志
tail -f logs/ruoyi-admin.log | grep -i performance
```

## 🔄 定时任务

监控系统包含以下定时任务：

1. **系统监控检查**：每分钟执行一次
2. **性能指标记录**：每5分钟记录一次
3. **性能计数器重置**：每小时重置一次
4. **告警统计重置**：每天凌晨2点重置

## 📞 技术支持

如有问题，请联系：
- 邮箱：<EMAIL>
- 文档：查看项目README.md
- 日志：查看系统日志文件
