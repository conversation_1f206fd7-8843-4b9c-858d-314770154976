# 🚀 监控系统部署指南

## 📋 部署清单

### ✅ 已完成的功能
- [x] 后端监控API (11个接口)
- [x] Swagger文档集成
- [x] 告警系统 (6种告警类型)
- [x] 性能监控 (内存、数据库、Redis)
- [x] 权限管理 (9个权限点)
- [x] 配置管理 (YAML配置)
- [x] 前端测试工具
- [x] 监控仪表板模板
- [x] 数据持久化方案

### 🔄 部署步骤

## 1. 数据库初始化

### 1.1 执行权限初始化
```bash
# 进入项目目录
cd /path/to/RuoYi-Vue-master

# 执行权限初始化SQL
mysql -u root -p ry-vue < sql/monitor_permissions.sql

# 验证权限是否正确添加
mysql -u root -p ry-vue < sql/verify_monitor_permissions.sql
```

### 1.2 创建监控数据表（可选）
```bash
# 如果需要监控数据持久化，执行以下SQL
mysql -u root -p ry-vue < sql/monitor_data_tables.sql
```

## 2. 应用配置

### 2.1 检查配置文件
确认 `ruoyi-admin/src/main/resources/application.yml` 包含监控配置：

```yaml
# 监控系统配置
ruoyi:
  monitor:
    enabled: true
    alert:
      memory:
        threshold: 85
      response:
        threshold: 5000
      error:
        threshold: 10
      email:
        enabled: false
        to: <EMAIL>
        from: <EMAIL>

# 邮件配置（可选）
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-auth-code
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
```

### 2.2 邮件配置（可选）
如果需要邮件告警功能，请配置邮件服务器：

1. **QQ邮箱配置示例：**
```yaml
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-authorization-code  # QQ邮箱授权码
```

2. **163邮箱配置示例：**
```yaml
spring:
  mail:
    host: smtp.163.com
    port: 25
    username: <EMAIL>
    password: your-authorization-code
```

3. **企业邮箱配置示例：**
```yaml
spring:
  mail:
    host: smtp.exmail.qq.com
    port: 587
    username: <EMAIL>
    password: your-password
```

## 3. 编译和启动

### 3.1 编译项目
```bash
# 清理并编译
mvn clean compile -DskipTests

# 打包应用
mvn package -DskipTests
```

### 3.2 启动应用
```bash
# 方式1：使用Maven启动
mvn spring-boot:run -pl ruoyi-admin

# 方式2：使用JAR包启动
java -jar ruoyi-admin/target/ruoyi-admin.jar

# 方式3：指定配置文件启动
java -jar ruoyi-admin/target/ruoyi-admin.jar --spring.profiles.active=prod
```

## 4. 功能验证

### 4.1 API测试
1. 打开浏览器访问：`http://localhost:8080/swagger-ui/index.html`
2. 查看监控相关API文档
3. 使用测试工具：打开 `test/monitor-api-test.html`

### 4.2 监控仪表板
1. 打开 `frontend/monitor-dashboard.html`
2. 配置服务器地址：`http://localhost:8080`
3. 测试各项监控功能

### 4.3 权限验证
1. 登录系统管理员账号
2. 检查是否有"系统监控"菜单
3. 验证各监控功能的访问权限

## 5. 生产环境部署

### 5.1 环境要求
- **Java**: JDK 8+
- **数据库**: MySQL 5.7+
- **Redis**: 3.0+
- **内存**: 建议2GB+
- **磁盘**: 建议10GB+

### 5.2 生产配置调优
```yaml
# application-prod.yml
ruoyi:
  monitor:
    enabled: true
    alert:
      memory:
        threshold: 80  # 生产环境建议更严格的阈值
      response:
        threshold: 3000  # 生产环境响应时间要求更高
      email:
        enabled: true  # 生产环境启用邮件告警
        to: <EMAIL>
        from: <EMAIL>

# 日志配置
logging:
  level:
    com.ruoyi.framework.monitor: INFO
  file:
    name: logs/monitor.log
    max-size: 100MB
    max-history: 30
```

### 5.3 监控数据清理
设置定时任务清理过期数据：
```sql
-- 每天凌晨2点执行数据清理
-- 可以通过crontab或Spring定时任务实现
CALL CleanOldMonitorData();
```

## 6. 扩展功能

### 6.1 集成更多告警通道

#### 钉钉告警集成
```java
// 在AlertManager中添加钉钉通知方法
public void sendDingTalkAlert(String message) {
    // 实现钉钉机器人告警
}
```

#### 企业微信告警集成
```java
// 在AlertManager中添加企业微信通知方法
public void sendWeChatAlert(String message) {
    // 实现企业微信告警
}
```

### 6.2 添加更多监控指标

#### CPU监控
```java
// 在PerformanceMonitor中添加CPU监控
public double getCpuUsage() {
    OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
    return osBean.getProcessCpuLoad() * 100;
}
```

#### 磁盘监控
```java
// 在PerformanceMonitor中添加磁盘监控
public Map<String, Object> getDiskUsage() {
    File[] roots = File.listRoots();
    Map<String, Object> diskInfo = new HashMap<>();
    // 实现磁盘使用率监控
    return diskInfo;
}
```

### 6.3 前端页面开发
基于提供的API接口，可以开发：
1. **Vue.js监控页面**
2. **React监控仪表板**
3. **移动端监控应用**

## 7. 故障排查

### 7.1 常见问题

#### 问题1：监控API返回401未授权
**解决方案：**
1. 检查用户是否有对应权限
2. 确认权限初始化SQL是否执行成功
3. 重新登录获取最新权限

#### 问题2：邮件告警发送失败
**解决方案：**
1. 检查邮件服务器配置
2. 验证邮箱账号和授权码
3. 检查网络连接和防火墙设置

#### 问题3：监控数据不更新
**解决方案：**
1. 检查定时任务是否正常运行
2. 查看应用日志是否有异常
3. 验证数据库连接是否正常

### 7.2 日志查看
```bash
# 查看监控相关日志
tail -f logs/ruoyi-admin.log | grep -i monitor

# 查看告警日志
tail -f logs/ruoyi-admin.log | grep -i alert

# 查看错误日志
tail -f logs/ruoyi-admin.log | grep -i error
```

## 8. 性能优化

### 8.1 数据库优化
```sql
-- 为监控表创建合适的索引
CREATE INDEX idx_performance_time ON sys_monitor_performance(record_time);
CREATE INDEX idx_alert_time_type ON sys_monitor_alert(alert_time, alert_type);
```

### 8.2 缓存优化
```java
// 在监控服务中使用缓存减少数据库查询
@Cacheable(value = "monitor", key = "'performance:' + #root.methodName")
public Map<String, Object> getSystemMetrics() {
    // 监控数据获取逻辑
}
```

### 8.3 异步处理
```java
// 使用异步方式发送告警，避免阻塞主线程
@Async
public void sendAlertAsync(String message) {
    // 异步发送告警
}
```

## 9. 安全考虑

### 9.1 权限控制
- 严格控制监控数据的访问权限
- 定期审查用户权限分配
- 记录监控操作日志

### 9.2 数据保护
- 敏感监控数据加密存储
- 定期备份监控配置
- 限制监控数据的导出功能

## 10. 维护计划

### 10.1 日常维护
- **每日**：检查告警状态和系统健康
- **每周**：清理过期监控数据
- **每月**：分析监控趋势和性能报告
- **每季度**：优化监控配置和阈值

### 10.2 升级计划
- 定期更新监控组件版本
- 扩展监控指标和告警类型
- 优化监控性能和用户体验

---

## 📞 技术支持

如有问题，请参考：
- 📚 [监控系统使用指南](monitor-system-guide.md)
- 🔧 [API文档](http://localhost:8080/swagger-ui/index.html)
- 📊 [监控仪表板](frontend/monitor-dashboard.html)
- 🧪 [API测试工具](test/monitor-api-test.html)

**部署完成后，您将拥有一个功能完整的企业级监控系统！** 🎉
