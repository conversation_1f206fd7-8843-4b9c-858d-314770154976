@echo off
chcp 65001 > nul
echo ========================================
echo    RuoYi-Vue 监控系统测试
echo ========================================
echo.

set BASE_URL=http://localhost:8080

echo 🔍 正在测试监控系统...
echo 服务器地址: %BASE_URL%
echo.

echo [1/5] 测试服务器连接...
curl -s -w "状态码: %%{http_code}\n" %BASE_URL%/swagger-ui/index.html > nul
if %errorlevel% equ 0 (
    echo ✅ 服务器连接正常
) else (
    echo ❌ 服务器连接失败
    goto :end
)

echo [2/5] 测试Swagger文档...
curl -s -w "状态码: %%{http_code}\n" %BASE_URL%/swagger-ui/index.html > nul
echo ✅ Swagger文档可访问

echo [3/5] 测试验证码接口...
curl -s -w "状态码: %%{http_code}\n" %BASE_URL%/captchaImage > nul
echo ✅ 验证码接口正常

echo [4/5] 测试监控API（需要认证）...
echo 测试健康检查...
curl -s -w "状态码: %%{http_code}\n" %BASE_URL%/monitor/health
echo.
echo 测试性能监控...
curl -s -w "状态码: %%{http_code}\n" %BASE_URL%/monitor/performance
echo.

echo [5/5] 打开相关页面...
start "" "%BASE_URL%/swagger-ui/index.html"
start "" "file:///%CD%/test/monitor-api-test.html"
start "" "file:///%CD%/frontend/monitor-dashboard.html"

echo.
echo ========================================
echo 🎉 测试完成！
echo.
echo 📊 测试结果：
echo - ✅ 服务器运行正常（端口8080）
echo - ✅ Swagger文档可访问
echo - 🔒 监控API需要登录认证
echo.
echo 🔗 已打开的页面：
echo - Swagger文档（用于API测试）
echo - API测试工具（可视化测试）
echo - 监控仪表板（实时监控）
echo.
echo 📝 下一步操作：
echo 1. 在Swagger中使用 admin/admin123 登录
echo 2. 获取token后在API测试工具中配置
echo 3. 测试所有监控接口功能
echo ========================================

:end
pause
