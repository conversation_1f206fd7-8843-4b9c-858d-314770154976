<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监控系统API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .api-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }
        .api-title {
            color: #333;
            margin-bottom: 10px;
            font-size: 18px;
            font-weight: bold;
        }
        .api-url {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            font-family: monospace;
            margin-bottom: 10px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .config-section {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .config-input {
            width: 200px;
            padding: 5px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 监控系统API测试工具</h1>
        
        <div class="config-section">
            <h3>配置</h3>
            <label>服务器地址:
                <input type="text" id="baseUrl" class="config-input" value="http://localhost:8080" placeholder="http://localhost:8080">
            </label>
            <label>Token: 
                <input type="text" id="token" class="config-input" placeholder="Bearer token (可选)">
            </label>
            <button class="test-button" onclick="testAllApis()">🚀 测试所有API</button>
        </div>

        <!-- 性能监控API -->
        <div class="api-section">
            <div class="api-title">📊 性能监控</div>
            <div class="api-url">GET /monitor/performance</div>
            <button class="test-button" onclick="testApi('/monitor/performance', 'performance')">测试</button>
            <div id="performance-result" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">📋 性能报告</div>
            <div class="api-url">GET /monitor/performance/report</div>
            <button class="test-button" onclick="testApi('/monitor/performance/report', 'performance-report')">测试</button>
            <div id="performance-report-result" class="result" style="display:none;"></div>
        </div>

        <!-- 健康检查API -->
        <div class="api-section">
            <div class="api-title">💚 健康检查</div>
            <div class="api-url">GET /monitor/health</div>
            <button class="test-button" onclick="testApi('/monitor/health', 'health')">测试</button>
            <div id="health-result" class="result" style="display:none;"></div>
        </div>

        <!-- 告警管理API -->
        <div class="api-section">
            <div class="api-title">🚨 告警统计</div>
            <div class="api-url">GET /monitor/alert/statistics</div>
            <button class="test-button" onclick="testApi('/monitor/alert/statistics', 'alert-statistics')">测试</button>
            <div id="alert-statistics-result" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">⚙️ 告警配置</div>
            <div class="api-url">GET /monitor/alert/config</div>
            <button class="test-button" onclick="testApi('/monitor/alert/config', 'alert-config')">测试</button>
            <div id="alert-config-result" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">🧪 测试告警</div>
            <div class="api-url">POST /monitor/alert/test</div>
            <button class="test-button" onclick="testApi('/monitor/alert/test', 'alert-test', 'POST')">测试</button>
            <div id="alert-test-result" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">🔍 手动检查</div>
            <div class="api-url">POST /monitor/alert/check</div>
            <button class="test-button" onclick="testApi('/monitor/alert/check', 'alert-check', 'POST')">测试</button>
            <div id="alert-check-result" class="result" style="display:none;"></div>
        </div>

        <!-- 资源监控API -->
        <div class="api-section">
            <div class="api-title">💾 资源监控</div>
            <div class="api-url">GET /monitor/resource</div>
            <button class="test-button" onclick="testApi('/monitor/resource', 'resource')">测试</button>
            <div id="resource-result" class="result" style="display:none;"></div>
        </div>

        <!-- 实时监控API -->
        <div class="api-section">
            <div class="api-title">📈 实时监控</div>
            <div class="api-url">GET /monitor/realtime</div>
            <button class="test-button" onclick="testApi('/monitor/realtime', 'realtime')">测试</button>
            <div id="realtime-result" class="result" style="display:none;"></div>
        </div>

        <!-- Swagger文档 -->
        <div class="api-section">
            <div class="api-title">📚 Swagger文档</div>
            <div class="api-url">GET /swagger-ui/index.html</div>
            <button class="test-button" onclick="openSwagger()">打开Swagger</button>
        </div>
    </div>

    <script>
        function getBaseUrl() {
            return document.getElementById('baseUrl').value.trim() || 'http://localhost:8080';
        }

        function getToken() {
            return document.getElementById('token').value.trim();
        }

        function getHeaders() {
            const headers = {
                'Content-Type': 'application/json'
            };
            const token = getToken();
            if (token) {
                headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
            }
            return headers;
        }

        async function testApi(endpoint, resultId, method = 'GET') {
            const resultDiv = document.getElementById(resultId + '-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '🔄 测试中...';

            try {
                const url = getBaseUrl() + endpoint;
                const response = await fetch(url, {
                    method: method,
                    headers: getHeaders()
                });

                const data = await response.text();
                let jsonData;
                try {
                    jsonData = JSON.parse(data);
                } catch (e) {
                    jsonData = data;
                }

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功 (${response.status})\n\n${JSON.stringify(jsonData, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 失败 (${response.status})\n\n${JSON.stringify(jsonData, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误\n\n${error.message}`;
            }
        }

        async function testAllApis() {
            const apis = [
                ['/monitor/performance', 'performance'],
                ['/monitor/performance/report', 'performance-report'],
                ['/monitor/health', 'health'],
                ['/monitor/alert/statistics', 'alert-statistics'],
                ['/monitor/alert/config', 'alert-config'],
                ['/monitor/resource', 'resource'],
                ['/monitor/realtime', 'realtime']
            ];

            for (const [endpoint, resultId] of apis) {
                await testApi(endpoint, resultId);
                await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms
            }
        }

        function openSwagger() {
            const url = getBaseUrl() + '/swagger-ui/index.html';
            window.open(url, '_blank');
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('监控系统API测试工具已加载');
            console.log('请确保后端服务已启动并可访问');
        };
    </script>
</body>
</html>
